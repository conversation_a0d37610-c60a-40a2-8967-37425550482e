import { getAssetDetail } from '@quantum/app/services/asset/asset.api';
import { Market } from '@quantum/app/services/asset/asset.types';
import { Metadata } from 'next';
import DetailPage from 'next-app/components/pages/detailPage/DetailPage';
import { getSymbolDetailMetadata } from '@/stationGroupConfig/metadata';
import { headers } from 'next/headers';


export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { market: string; symbol: string, lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const headersList = await headers()
  const xForwardedHost = headersList.get("x-forwarded-host");
  const {
    marketNameLangMap,
    pageTitleLangMap
  } = getSymbolDetailMetadata(xForwardedHost as any)
  const lang = params.lang || 'en';
  return { title: `${params.symbol}${marketNameLangMap[params.market as Market][lang]}${pageTitleLangMap[lang]}` };

}


async function fetchPageData(params: { market: string; symbol: string }) {
  try {
    const res = await getAssetDetail({
      market: params.market as Market,
      symbol: params.symbol,
    });
    return {
      info: res,
    };
  } catch (error) {
    return {
      info: null,
    };
  }
}
export default async function DetailScreen({ params }: { params: { market: string; symbol: string } }) {
  const {info} = await fetchPageData(params);
  return (
    <>
      <DetailPage defaultData={info} market={params.market as Market} symbol={params.symbol} />
    </>
  );
}
