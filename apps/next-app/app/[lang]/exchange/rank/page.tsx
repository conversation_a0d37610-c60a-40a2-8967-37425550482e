import ExchangeRank from '@quantum/app/screens/exchange/rank/ExchangeRank';
import ScrollMessageBar from '@quantum/app/components/ScrollMessageBar';
import { getExchangeList } from '@quantum/app/services/crypto_exchange/crypto_exchange.api';
import axios from 'axios';
import { Metadata } from 'next';
import { headers } from 'next/headers';
import { getExchangeRankMetadata } from '@/stationGroupConfig/metadata';

async function getExchangeUrlList() {
  const isGray = process.env.SETUP_ENV === 'gray';

  const env = isGray ? 'gray' : process.env.APP_ENV;
  try {
    const res = await axios.get('http://qqtoa.ulegofi.com/api/exchange_url:list', {
      headers: {
        Authorization: `Bearer ${process.env.OA_API_KEY}`,
      },
      params: {
        pageSize: 1000,
        page: 1,
        filter: { $and: [{ enable_env: { $anyOf: [env] } }] },
      },
    });

    const list = res.data.data as {
      exchange_name: string;
      exchange_url: string;
    }[];
    return list;
  } catch (error) {
    console.error(error);
    return [];
  }
}

const fetchData = async () => {
  try {
    const [exchangeList, exchangeUrlList] = await Promise.all([getExchangeList(), getExchangeUrlList()]);
    return exchangeList.map((exchange) => {
      const exchangeUrl = exchangeUrlList.find((url) => url.exchange_name === exchange.name);
      return {
        ...exchange,
        exchange_url: exchangeUrl?.exchange_url,
      };
    });
  } catch (error) {
    console.error(error);
    return [];
  }
};


export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const headersList = await headers();
  const xForwardedHost = headersList.get('x-forwarded-host');
  const { pageTitleLangMap } = getExchangeRankMetadata(xForwardedHost as any);
  const lang = params.lang || 'en';
  return { title: pageTitleLangMap[lang] };
}

export default async function ExchangeRankPage() {
  const exchangeList = await fetchData();
  return (
    <>
      <ScrollMessageBar />
      <ExchangeRank exchangeList={exchangeList} />
    </>
  );
}
