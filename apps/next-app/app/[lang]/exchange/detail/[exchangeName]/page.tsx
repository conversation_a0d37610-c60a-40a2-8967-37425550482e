import ExchangeDetail from '@quantum/app/screens/exchange/ExchangeDetail';
import { Metadata } from 'next';
import { headers } from 'next/headers';
import { getExchangeDetailMetadata } from '@/stationGroupConfig/metadata';


export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { lang?: 'zh-CN' | 'en'; exchangeName: string };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const headersList = await headers();
  const xForwardedHost = headersList.get('x-forwarded-host');
  const { pageTitleLangMap } = getExchangeDetailMetadata(xForwardedHost as any);
  const lang = params.lang || 'en';
  const exchangeName = params.exchangeName;
  return { title: exchangeName + pageTitleLangMap[lang] };
}

export default function ExchangeDetailPage({ params }: { params: { exchangeName: string } }) {
  return <ExchangeDetail exchangeName={params.exchangeName} />;
}
