import { headers } from 'next/headers';

import initTranslations, { i18nNamespaces } from '@/common/translations/initTranslations';
import PageLayout from '@/components/layout/PageLayout';
import { GoogleAnalytics } from '@next/third-parties/google';
import { ThemeProvider } from '@quantum/components/ui/ThemeProvider/ThemeProvider';
import StyledJsxRegistry from '../registry';
import { Inter } from 'next/font/google';
import Script from 'next/script';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});
import getConfig from 'next/config';

const { publicRuntimeConfig } = getConfig();

import { Metadata } from 'next';
import { getGaID } from '@/stationGroupConfig';
import { getLayoutMetadata } from '@/stationGroupConfig/metadata';
import { ServerInfoProvider } from '@/provider/ServerInfoProvider';

export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const headersList = await headers();
  const xForwardedHost = headersList.get('x-forwarded-host');
  const { pageTitleLangMap, pageDescriptionLangMap, pageKeywordsLangMap, openGraphImage } = getLayoutMetadata(
    xForwardedHost as any,
  );

  const lang = params.lang || 'en';
  return {
    title: pageTitleLangMap[lang],
    description: pageDescriptionLangMap[lang],
    keywords: pageKeywordsLangMap[lang].split(','),
    openGraph: {
      title: pageTitleLangMap[lang],
      description: pageDescriptionLangMap[lang],
      images: [
        {
          url: openGraphImage,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      images: [
        {
          url: openGraphImage,
        },
      ],
    },
    metadataBase: new URL(`https://${xForwardedHost}/`),
    alternates: {
      canonical: '/',
      languages: {
        'en-US': '/en',
        'zh-CN': '/zh-CN',
      },
    },
  };
}

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: { lang: string };
}>) {
  const headersList = await headers();

  const xForwardedHost = headersList.get('x-forwarded-host');
  const currentDomain = headersList.get('x-current-host');
  const staticHost = headersList.get('x-static-host');
  const gaId = getGaID(xForwardedHost);
  const { lang = 'en' } = params;
  const { resources } = await initTranslations(lang, i18nNamespaces, xForwardedHost || '');

  return (
    <html lang={lang} className={inter.className}>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <meta name="google-site-verification" content="XdVnVciB8W3pW9VY3kIJH-kq8s8Jk8UQTP-0WeFo1Wc" />
        <meta name="etag" content={publicRuntimeConfig.ETAG} />
        {process.env.APP_ENV === 'production' && !!gaId && <GoogleAnalytics gaId={gaId} />}
        <Script
          id="env"
          dangerouslySetInnerHTML={{
            __html: `
            window.__ENV__ = {
              APP_ENV: '${process.env.APP_ENV}',
              SETUP_ENV: '${process.env.SETUP_ENV}',
              VERSION: '${process.env.VERSION}',
              currentDomain: '${currentDomain}',
              xForwardedHost: '${xForwardedHost}',
            }
            `,
          }}
        />
      </head>

      <body>
        <ServerInfoProvider domain={xForwardedHost || ''} staticDomain={`https://${staticHost || ''}`}>
          <StyledJsxRegistry>
            <ThemeProvider>
              <PageLayout i18nResources={resources} lang={lang} currentDomain={xForwardedHost || ''}>
                {children}
              </PageLayout>
            </ThemeProvider>
          </StyledJsxRegistry>
        </ServerInfoProvider>
      </body>
    </html>
  );
}
