import HomeScreen from '@quantum/app/screens/home';
import { getMarketOverview } from '@quantum/app/services/asset/asset.api';
import { Metadata } from 'next';
import { headers } from 'next/headers';
import { getMarketMetadata } from '@/stationGroupConfig/metadata';

async function fetchPageData() {
  try {
    const res = await getMarketOverview();
    return {
      overviewData: res,
    };
  } catch (error) {
    console.error(error);
    return {
      overviewData: null,
    };
  }
}


export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const headersList = await headers();
  const xForwardedHost = headersList.get('x-forwarded-host');
  const { pageTitleLangMap } = getMarketMetadata(xForwardedHost as any);
  const lang = params.lang || 'en';
  return { title: pageTitleLangMap[lang] };
}

export default async function HomePage() {
  const data = await fetchPageData();
  return <HomeScreen overviewData={data.overviewData} />;
}
