import { getReportList } from '@quantum/app/components/ResearchReport';
import ResearchReportListPageCom from '@quantum/app/components/ResearchReport/ResearchReportListPageCom';
import { Metadata } from 'next';
import { headers } from 'next/headers';
import { getResearchReportMetadata } from '@/stationGroupConfig/metadata';


  export async function generateMetadata({
    params,
    searchParams,
  }: {
    params: { id: string; lang?: 'zh-CN' | 'en' };
    searchParams: { [key: string]: string | string[] | undefined };
  }): Promise<Metadata> {
    const headersList = await headers()
    const xForwardedHost = headersList.get("x-forwarded-host");
    const { pageTitleLangMap } = getResearchReportMetadata(xForwardedHost as any)
    const lang = params.lang || 'en';
  
    return { title: `${pageTitleLangMap[lang]}` };
  }


async function fetchPageData() {
  const list = await getReportList({
    pageSize: 1000,
  });
  return list || [];
}

export default async function ResearchReportListPage() {
  const list = await fetchPageData();
  return <ResearchReportListPageCom list={list} />;
}
