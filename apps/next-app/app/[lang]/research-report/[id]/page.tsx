import { getReportList } from '@quantum/app/components/ResearchReport';
import ResearchReportDetailIndex from '@quantum/app/components/ResearchReport/ResearchReportDetailIndex';
import { Metadata } from 'next';
import { headers } from 'next/headers';
import { getResearchReportMetadata } from '@/stationGroupConfig/metadata';

export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { id: string; lang?: 'zh-CN' | 'en' };
  searchParams: { [key: string]: string | string[] | undefined };
}): Promise<Metadata> {
  const headersList = await headers()
  const xForwardedHost = headersList.get("x-forwarded-host");
  const { pageTitleLangMap } = getResearchReportMetadata(xForwardedHost as any)
  const data = await fetchPageData(params)
  const lang = params.lang || 'en';

  return { title: `${data.date} ${pageTitleLangMap[lang]}` };
}

async function fetchPageData(params: { id: string }) {
  const res = await getReportList({
    date: params.id,
  });
  return res[0] || null;
}



export default async function ResearchReportDetailPage({ params }: { params: { id: string } }) {
  const res = await fetchPageData(params);
  return <ResearchReportDetailIndex detail={res} />;
}
