/** @type {import('next').NextConfig} */
const { DefinePlugin } = require('webpack');

let etag_revision = '';
try {
  etag_revision = require('child_process').execSync('git rev-parse HEAD').toString().trim();
} catch (err) {}
etag_revision = etag_revision || process.env.commitHash;

const withGluestackUI = require('@gluestack/ui-next-adapter');
const nextConfig = {
  reactStrictMode: true,
  transpilePackages: [
    '@gluestack-ui/nativewind-utils',
    'nativewind',
    'react-native-css-interop',
    'react-native-marked',
    'react-native-svg',
    '@react-native-community/datetimepicker',
  ],
  images: {
    domains: [
      'static.wuba.com',
      'assets.coingecko.com',
      'fpoimg.com',
      'dummyimage.com',
      's3.tradingview.com',
      'via.placeholder.com',
      'static.stockbits.ai',
      'static.tokenrader.com',
      'static.bibay.xyz',
      'static.tallnex.com',
      's2.coinmarketcap.com',
    ],
  },
  webpack: (config) => {
    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      // Transform all direct `react-native` imports to `react-native-web`
      'react-native$': 'react-native-web',
    };
    config.resolve.alias['@unitools/navigation'] = 'next/navigation';
    config.resolve.alias['@unitools/router'] = '@unitools/router-next';
    config.resolve.alias['@unitools/image'] = '@unitools/image-next';
    config.resolve.alias['@unitools/link'] = '@unitools/link-next';
    config.resolve.extensions = ['.web.js', '.web.jsx', '.web.ts', '.web.tsx', ...config.resolve.extensions];

    config.plugins.push(
      new DefinePlugin({
        __DEV__: JSON.stringify(process.env.NODE_ENV !== 'production'),
      }),
    );

    return config;
  },
  typescript: { ignoreBuildErrors: true },
  publicRuntimeConfig: {
    ETAG: etag_revision,
  },
  async rewrites() {
    return {
      beforeFiles: [
        // These rewrites are checked after headers/redirects
        // and before all files including _next/public files which
        // allows overriding page files
        {
          source: '/backend-api/:path*',
          // destination: 'http://127.0.0.1:4523/m1/6178450-0-default/:path*',

          destination: 'https://stockbits-test.3bodylabs.com/:path*', // The :path parameter is used here so will not be automatically passed in the query
          // destination: 'https://api.stockbits.ai/:path*', // The :path parameter is used here so will not be automatically passed in the query
        },
      ],
      afterFiles: [
        // These rewrites are checked after pages/public files
        // are checked but before dynamic routes
      ],
      fallback: [
        // These rewrites are checked after both pages/public files
        // and dynamic routes are checked
      ],
    };
  },
};

// module.exports = (nextConfig)
// 使用 withGluestackUI 包装 nextConfig，但是终端会有警告
module.exports = withGluestackUI.withGluestackUI(nextConfig);
