{"detail": {"tabs": {"data": "Data", "intro": "Introduction", "liquidity": "Liquidity", "volume": "Volume", "reserves": "Reserves"}, "spot_liquidity": "Spot Liquidity", "exchange_volume": "Exchange Volume", "Crypto_Exchanges_Assets_Transparency": "Crypto Exchanges Assets Transparency"}, "header": {"breadcrumb": {"crypto_exchange": "Cryptocurrency Exchanges"}, "centralized_exchange": "Centralized Exchange", "go_to_trade": "Go to Trade"}, "trading_stats": {"title": "Trading and Position Statistics", "spot_volume_24h": "24h Spot Volume", "futures_volume_24h": "24h Futures Volume", "futures_position": "Futures Positions"}, "contract_data": {"title": "24h Contract Data", "liquidation_24h": "24h Liquidation", "long_liquidation": "Long Liquidation", "short_liquidation": "Short Liquidation"}, "info": {"title": "Introduction", "spot_volume_24h": "24h Spot Volume", "total_assets": "Total Assets"}, "liquidity": {"title": "Liquidity", "reported_volume": "Reported Volume", "currency_count": "Currency Count", "pair_count": "Trading Pair Count", "trust_score": "Total Trust Score"}, "liquidity_table": {"no": "NO.", "currency": "<PERSON><PERSON><PERSON><PERSON>", "pair": "Trading Pair", "price": "Price", "depth_up": "+2% Depth", "depth_down": "-2% Depth", "volume": "Volume", "spread": "Spread (%)"}, "rank": {"tabs": {"spot": "Spot"}, "legal_tender": {"all": "All", "label": "Legal Tender"}, "title": "Cryptocurrency Exchange Ranking", "description": "Data comes from third-party data platforms such as coingecko, coinmarketcap, coinglass, etc., and ranks and scores exchanges based on reasonable credibility.", "table": {"no": "NO.", "exchange": "Exchange", "volume_24h": "24h Volume", "btc_balance": "BTC Wallet Balance", "pair_count": "Pairs Count", "weekly_visits": "Weekly Visits", "maker_fee": "Maker Fee", "trust_score": "Trust Score", "field": "Exchange index", "default_sort": "<PERSON><PERSON><PERSON>", "desc": "Desc", "asc": "Asc"}}, "volume_chart": {"time_range": {"24h": "24 Hours", "7d": "7 Days", "14d": "14 Days", "1m": "1 Month", "3m": "3 Months", "1y": "1 Year"}}, "volume_table": {"no": "NO.", "currency": "<PERSON><PERSON><PERSON><PERSON>", "address": "Address", "balance": "Balance", "value": "Value"}, "rank_tab": {"volume_24h": "24h Volume", "btc_balance": "BTC Wallet Balance", "pair_count": "Pairs Count", "weekly_visits": "Weekly Visits", "maker_fee": "Maker Fee"}}