'use client';

import React, { createContext, useContext } from 'react';

type Mode = 'light' | 'dark' | 'system';
type Theme = "light" | "dark" | undefined;

interface ServerInfoContextType {
  domain: string;
  staticDomain: string;
}

export const ServerInfoContext = createContext<ServerInfoContextType | undefined>(
  undefined
);

export const ServerInfoProvider = ({ children, domain, staticDomain }: { children: React.ReactNode, domain:string, staticDomain:string }) => {
  
  return (
    <ServerInfoContext.Provider value={{ domain, staticDomain }}>
      {children}
    </ServerInfoContext.Provider>
  );
};

export const useServerInfo = () => {
  const context = useContext(ServerInfoContext);
  if (context === undefined) {
    throw new Error("useServerInfo must be used within a ServerInfoProvider");
  }
  return context;
};