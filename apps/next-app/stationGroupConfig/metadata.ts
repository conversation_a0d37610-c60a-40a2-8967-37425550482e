type TLanguage = {
  'zh-CN': string;
  'en': string;
}

type TData = {
  pageTitleLangMap: TLanguage,
  pageDescriptionLangMap?: TLanguage,
  pageKeywordsLangMap?: TLanguage,
  openGraphImage?: string,
  [_: string]: any
}

type TDataInfo = {
  'localhost:3000': TData,
  'as-node.3bodylabs.com': TData,
  'stockbits.ai': TData,
  'tokenrader.com': TData,
  'tallnex.com': TData,
  'bibay.xyz': TData,
}


const stockbitsLayoutData = {
  pageTitleLangMap: {
    'zh-CN': 'Stockbits | 股票和加密市场行情,分析和交易观点',
    en: 'Stockbits | Stock&Crypto Markets Price Tracking, Analysis,Trading Ideas',
  },
  pageDescriptionLangMap: {
    'zh-CN': `StockBits is the smartest and free one-stop platform for stock & crypto tracking, analysis, and community trading.
Follow, Track, Trade, Earn on Stock and Crypto Market with StockBits.`,
    en: `Stockbits is the smartest and free one-stop platform for stock & crypto tracking, analysis, and community trading.
Follow, Track, Trade, Earn on Stock and Crypto Market with StockBits.`,
  },
  pageKeywordsLangMap: {
    'zh-CN': `Stockbits,Stockbits行情工具,StockBits币股行情,币股行情工具,股票市场,股票价格,股票跟踪,股票分析,股票交易观点,加密市场,加密货币价格,加密跟踪,加密分析,加密交易观点,交易工具,免费交易工具,股票交易工具,加密交易工具`,
    en: `best free portfolio tracker for stocks and crypto, how to track crypto and stocks in one place,stock and crypto portfolio management tool,how to copy pro traders’ stock picks,free crypto trading signals from top traders,best platform to follow stock trading ideas,real-time crypto trading signals from traders,free tool to analyze crypto and stock trends`,
  },
  openGraphImage: 'https://stockbits.ai/assets/share.png',
};
const layoutDataMap: TDataInfo = {
  'localhost:3000': stockbitsLayoutData,
  'as-node.3bodylabs.com': stockbitsLayoutData,
  'stockbits.ai': stockbitsLayoutData,
  'tokenrader.com': {
    pageTitleLangMap: {
      'zh-CN': 'tokenrader.com[待补充]',
      en: 'tokenrader.com[待补充]',
    },
    pageDescriptionLangMap: {
      'zh-CN': 'tokenrader.com[待补充]',
      en: 'tokenrader.com[待补充]',
    },
    pageKeywordsLangMap: {
      'zh-CN': 'tokenrader.com[待补充]',
      en: 'tokenrader.com[待补充]',
    },
    openGraphImage: '',
  },
  'bibay.xyz': {
    pageTitleLangMap: {
      'zh-CN': 'bibay.xyz[待补充]',
      en: 'bibay.xyz[待补充]',
    },
    pageDescriptionLangMap: {
      'zh-CN': 'bibay.xyz[待补充]',
      en: 'bibay.xyz[待补充]',
    },
    pageKeywordsLangMap: {
      'zh-CN': 'bibay.xyz[待补充]',
      en: 'bibay.xyz[待补充]',
    },
    openGraphImage: '',
  },
  'tallnex.com': {
    pageTitleLangMap: {
      'zh-CN': 'tallnex.com[待补充]',
      en: 'tallnex.com[待补充]',
    },
    pageDescriptionLangMap: {
      'zh-CN': 'tallnex.com[待补充]',
      en: 'tallnex.com[待补充]',
    },
    pageKeywordsLangMap: {
      'zh-CN': 'tallnex.com[待补充]',
      en: 'tallnex.com[待补充]',
    },
    openGraphImage: '',
  },
};

export function getLayoutMetadata(domain: keyof typeof layoutDataMap) {
  return layoutDataMap[domain];
}

const stockbitsSymbolDetailData = {
  marketNameLangMap: {
    US: {
      'zh-CN': ' 股票',
      en: ' Stock',
    },
    HK: {
      'zh-CN': '股票',
      en: ' Stock',
    },
    CRYPTO: {
      'zh-CN': '加密货币',
      en: ' Crypto',
    },
  },
  pageTitleLangMap: {
    'zh-CN': '实时价格，市值和交易观点',
    en: ' Live Price ,Market Cap,Trading Ideas',
  },
};
const symbolDetailDataMap: TDataInfo = {
  'localhost:3000': stockbitsSymbolDetailData,
  'as-node.3bodylabs.com': stockbitsSymbolDetailData,
  'stockbits.ai': stockbitsSymbolDetailData,
  'tokenrader.com': {
    marketNameLangMap: stockbitsSymbolDetailData.marketNameLangMap,
    pageTitleLangMap: {
      'zh-CN': 'tokenrader.com[待补充]',
      en: 'tokenrader.com[待补充]',
    },
  },
  'bibay.xyz': {
    marketNameLangMap: stockbitsSymbolDetailData.marketNameLangMap,
    pageTitleLangMap: {
      'zh-CN': 'bibay.xyz[待补充]',
      en: 'bibay.xyz[待补充]',
    },
  },
  'tallnex.com': {
    marketNameLangMap: stockbitsSymbolDetailData.marketNameLangMap,
    pageTitleLangMap: {
      'zh-CN': 'tallnex.com[待补充]',
      en: 'tallnex.com[待补充]',
    },
  },
};

export function getSymbolDetailMetadata(domain: keyof typeof symbolDetailDataMap) {
  return symbolDetailDataMap[domain];
}

const stockbitsExchangeDetailData = {
  pageTitleLangMap: {
    'zh-CN': ' | 股票和加密市场行情，分析和交易观点',
    en: ' | Stock&Crypto Markets Price Tracking, Analysis,Trading Ideas',
  },
};

const exchangeDetailDataMap: TDataInfo = {
  'localhost:3000': stockbitsExchangeDetailData,
  'as-node.3bodylabs.com': stockbitsExchangeDetailData,
  'stockbits.ai': stockbitsExchangeDetailData,
  'tokenrader.com': {
    pageTitleLangMap: {
      'zh-CN': 'tokenrader.com[待补充]',
      en: 'tokenrader.com[待补充]',
    },
  },
  'bibay.xyz': {
    pageTitleLangMap: {
      'zh-CN': 'bibay.xyz[待补充]',
      en: 'bibay.xyz[待补充]',
    },
  },
  'tallnex.com': {
    pageTitleLangMap: {
      'zh-CN': 'tallnex.com[待补充]',
      en: 'tallnex.com[待补充]',
    },
  },
};

export function getExchangeDetailMetadata(domain: keyof typeof exchangeDetailDataMap) {
  return exchangeDetailDataMap[domain];
}

const stockbitsExchangeRankData = {
  pageTitleLangMap: {
    'zh-CN': '加密货币交易所排名 | 股票和加密市场行情，分析和交易观点',
    en: 'Cryptocurrency Exchange Ranking | Stock&Crypto Markets Price Tracking, Analysis,Trading Ideas',
  },
};

const exchangeRankDataMap: TDataInfo = {
  'localhost:3000': stockbitsExchangeRankData,
  'as-node.3bodylabs.com': stockbitsExchangeRankData,
  'stockbits.ai': stockbitsExchangeRankData,
  'tokenrader.com': {
    pageTitleLangMap: {
      'zh-CN': 'tokenrader.com[待补充]',
      en: 'tokenrader.com[待补充]',
    },
  },
  'bibay.xyz': {
    pageTitleLangMap: {
      'zh-CN': 'bibay.xyz[待补充]',
      en: 'bibay.xyz[待补充]',
    },
  },
  'tallnex.com': {
    pageTitleLangMap: {
      'zh-CN': 'tallnex.com[待补充]',
      en: 'tallnex.com[待补充]',
    },
  },
};

export function getExchangeRankMetadata(domain: keyof typeof exchangeRankDataMap) {
  return exchangeRankDataMap[domain];
}

const stockbitsMarketData = {
  pageTitleLangMap: {
    'zh-CN': '加密货币交易所排名 | 股票和加密市场行情，分析和交易观点',
    en: 'Cryptocurrency Exchange Ranking | Stock&Crypto Markets Price Tracking, Analysis,Trading Ideas',
  },
};

const marketDataMap: TDataInfo = {
  'localhost:3000': stockbitsMarketData,
  'as-node.3bodylabs.com': stockbitsMarketData,
  'stockbits.ai': stockbitsMarketData,
  'tokenrader.com': {
    pageTitleLangMap: {
      'zh-CN': 'tokenrader.com[待补充]',
      en: 'tokenrader.com[待补充]',
    },
  },
  'bibay.xyz': {
    pageTitleLangMap: {
      'zh-CN': 'bibay.xyz[待补充]',
      en: 'bibay.xyz[待补充]',
    },
  },
  'tallnex.com': {
    pageTitleLangMap: {
      'zh-CN': 'tallnex.com[待补充]',
      en: 'tallnex.com[待补充]',
    },
  },
};

export function getMarketMetadata(domain: keyof typeof marketDataMap) {
  return marketDataMap[domain];
}

const stockbitsResearchReportData = {
  pageTitleLangMap: {
    'zh-CN': 'AI 研报',
    en: 'AI Research Report',
  },
};
const researchReportDataMap: TDataInfo = {
  'localhost:3000': stockbitsResearchReportData,
  'as-node.3bodylabs.com': stockbitsResearchReportData,
  'stockbits.ai': stockbitsResearchReportData,
  'tokenrader.com': {
    pageTitleLangMap: {
      'zh-CN': 'tokenrader.com[待补充]',
      en: 'tokenrader.com[待补充]',
    },
  },
  'bibay.xyz': {
    pageTitleLangMap: {
      'zh-CN': 'bibay.xyz[待补充]',
      en: 'bibay.xyz[待补充]',
    },
  },
  'tallnex.com': {
    pageTitleLangMap: {
      'zh-CN': 'tallnex.com[待补充]',
      en: 'tallnex.com[待补充]',
    },
  },
};

export function getResearchReportMetadata(domain: keyof typeof researchReportDataMap) {
  return researchReportDataMap[domain];
}

const stockbitsSignalData = {
  pageTitleLangMap: {
    'zh-CN': '免费的股票和加密市场分析，交易观点和新闻',
    en: 'Free Stock&Crypto Analysis,Trading Ideas&News',
  },
};

const signalDataMap: TDataInfo = {
  'localhost:3000': stockbitsSignalData,
  'as-node.3bodylabs.com': stockbitsSignalData,
  'stockbits.ai': stockbitsSignalData,
  'tokenrader.com': {
    pageTitleLangMap: {
      'zh-CN': 'tokenrader.com[待补充]',
      en: 'tokenrader.com[待补充]',
    },
  },
  'bibay.xyz': {
    pageTitleLangMap: {
      'zh-CN': 'bibay.xyz[待补充]',
      en: 'bibay.xyz[待补充]',
    },
  },
  'tallnex.com': {
    pageTitleLangMap: {
      'zh-CN': 'tallnex.com[待补充]',
      en: 'tallnex.com[待补充]',
    },
  },
};

export function getSignalMetadata(domain: keyof typeof signalDataMap) {
  return signalDataMap[domain];
}

