const glob = require('glob')
const util = require('util')
const path = require('path')
const fs = require('fs')
const _ = require('lodash')
const mkdirp = require('mkdirp')
const exec = util.promisify(require('child_process').exec)

const feishu = require('./lark-docs-util.js')

const { execQueue } = require('./queue')
const { isArray, isString } = require('lodash')
const cwd = process.cwd()

let translationsPath

// 预定义的网站标识符
const SITE_IDENTIFIERS = ['tokenrader', 'bibay', 'tallnex']

// const domains = i18nNamespace;

async function writeCSVData(remoteRows, locales, domain) {
  // 分离普通locales和各个站点的locales
  const normalLocales = []
  const siteLocalesMap = {}

  // 初始化各个站点的locales数组
  SITE_IDENTIFIERS.forEach(siteId => {
    siteLocalesMap[siteId] = []
  })

  locales.forEach((locale, index) => {
    if (locale) {
      // 检查是否匹配任何站点标识符
      let matched = false
      for (const siteId of SITE_IDENTIFIERS) {
        const sitePrefix = `site_${siteId}-`
        if (locale.startsWith(sitePrefix)) {
          // site_tokenrader-en -> en, site_tokenrader-zh-CN -> zh-CN
          const actualLocale = locale.replace(sitePrefix, '')
          siteLocalesMap[siteId].push({ locale: actualLocale, index, siteId })
          matched = true
          break
        }
      }

      // 如果不匹配任何站点标识符，则为普通locale
      if (!matched) {
        normalLocales.push({ locale, index })
      }
    }
  })

  // 处理普通locales (保存到 /public/locales/)
  await execQueue(
    normalLocales.map(({ locale, index }) => async () => {
      let actualLocale = locale
      if (locale === 'jp') {
        actualLocale = 'ja'
      }
      const filepath = path.join(
        translationsPath,
        actualLocale || '',
        `${domain}.json`
      )
      if (!actualLocale) return

      await writeLocaleData(remoteRows, index, filepath, actualLocale, 'normal')
    })
  )

  // 处理各个站点的locales
  for (const siteId of SITE_IDENTIFIERS) {
    const siteLocales = siteLocalesMap[siteId]
    if (siteLocales.length > 0) {
      const siteTranslationsPath = translationsPath.replace('/locales', `/locales_site_${siteId}`)
      await execQueue(
        siteLocales.map(({ locale, index }) => async () => {
          let actualLocale = locale
          if (locale === 'jp') {
            actualLocale = 'ja'
          }
          const filepath = path.join(
            siteTranslationsPath,
            actualLocale || '',
            `${domain}.json`
          )
          if (!actualLocale) return

          await writeLocaleData(remoteRows, index, filepath, actualLocale, `site_${siteId}`)
        })
      )
    }
  }
}

async function writeLocaleData(remoteRows, localeIndex, filepath, locale, type) {
  let data = {}
  if (!fs.existsSync(filepath)) {
    mkdirp.sync(path.parse(filepath).dir)
  }

  let stringsCount = 0
  remoteRows.slice(1).forEach((row) => {
    const k = row[0]
    let v = row[localeIndex + 1]
    if (isArray(v)) {
      v = v.map((_i) => _i.text).join('')
    }
    if (v && k) {
      const conflictValue = _.get(data, k)
      if (conflictValue && !_.isString(conflictValue)) {
        throw new Error(
          `I18N key conflict: [key:${k}];[value:${v}];[conflictValue:${JSON.stringify(
            conflictValue
          )}]}`
        )
      }
      _.set(data, k, v)
      stringsCount++
    }
  })

  fs.writeFileSync(
    filepath,
    JSON.stringify(data, null, 2)
  )

  console.log(
    `Write [${type}] [${locale}]: ${stringsCount} strings`
  )
}

async function main(app) {
  console.log(`Processing sites: ${SITE_IDENTIFIERS.join(', ')}`)

  const appPath = path.join(cwd, 'apps', app)
  translationsPath = path.join(appPath, 'public', 'locales')

  await feishu.getToken()

  const sheetKey = 'LWRKsETd8hIp3ut6CVxj9ZGepSe'
  const { sheets } = await feishu.getSpreadsheets(sheetKey)
  const list = sheets
  await execQueue(
    list.map((sheet, index) => async () => {
      const remoteRows = await feishu.readSheetRows(
        sheet.sheetId,
        sheet.rowCount,
        sheet.columnCount,
        sheetKey
      )
      const locales = remoteRows[0].slice(1)

      await writeCSVData(remoteRows, locales, sheet.title)

      // 生成日志信息，显示所有处理的站点路径
      const sitePaths = SITE_IDENTIFIERS.map(siteId =>
        `${appPath}/public/locales_site_${siteId}/[lng]/${sheet.title}.json`
      ).join(', ')

      console.log(
        `[Pull] ${appPath}/public/locales/[lng]/${sheet.title}.json and ${sitePaths} updated rows:${remoteRows.length}. (${
          index + 1
        }/${list.length})`
      )
    })
  )
}

module.exports = main

// 直接运行脚本时执行
if (require.main === module) {
  main('next-app')
}
