# I18n Pull Script 使用说明

## 概述

优化后的 `pull-i18n.js` 脚本支持同时处理多个预定义的站点标识符，自动识别和处理不同站点的国际化文件。

## 主要改进

1. **多站点支持**: 同时处理 `tokenrader`、`bibay`、`tallnex` 三个站点
2. **自动识别**: 脚本会自动识别表格中的站点字段并分类处理
3. **简化使用**: 无需手动指定参数，一次运行处理所有站点

## 预定义站点

脚本会自动处理以下站点标识符：
- `tokenrader`
- `bibay`
- `tallnex`

## 使用方式

### 直接运行

```bash
node scripts/lark/i18n/pull-i18n.js
```

脚本会自动处理所有预定义站点的字段：
- 表格字段: `site_tokenrader-en`, `site_bibay-zh-CN`, `site_tallnex-ja` 等
- 存放路径: `/public/locales_site_tokenrader/`, `/public/locales_site_bibay/`, `/public/locales_site_tallnex/`

### 在代码中调用

```javascript
const pullI18n = require('./scripts/lark/i18n/pull-i18n.js')

await pullI18n('next-app')
```

## 文件结构

脚本会生成以下文件结构：

```
apps/next-app/public/
├── locales/                    # 普通国际化文件
│   ├── en/
│   │   └── common.json
│   └── zh-CN/
│       └── common.json
├── locales_site_tokenrader/    # tokenrader 站点文件
│   ├── en/
│   │   └── common.json
│   └── zh-CN/
│       └── common.json
├── locales_site_bibay/         # bibay 站点文件
│   ├── en/
│   │   └── common.json
│   └── zh-CN/
│       └── common.json
└── locales_site_tallnex/       # tallnex 站点文件
    ├── en/
    │   └── common.json
    └── zh-CN/
        └── common.json
```

## 表格字段格式

脚本会自动识别以下格式的字段：

- **普通字段**: `en`, `zh-CN`, `ja` 等 → 保存到 `/public/locales/`
- **站点字段**:
  - `site_tokenrader-en`, `site_tokenrader-zh-CN` → 保存到 `/public/locales_site_tokenrader/`
  - `site_bibay-en`, `site_bibay-zh-CN` → 保存到 `/public/locales_site_bibay/`
  - `site_tallnex-en`, `site_tallnex-zh-CN` → 保存到 `/public/locales_site_tallnex/`

## 注意事项

- 表格中的字段格式必须为: `site_{站点标识符}-{语言代码}`
- 只有预定义的站点标识符 (`tokenrader`, `bibay`, `tallnex`) 会被处理
- 生成的目录路径格式为: `locales_site_{站点标识符}`
- 如果表格中没有某个站点的字段，对应的目录不会被创建
