import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { authStore } from '../store/auth.store';
import { userStore } from '../store/user.store';

// 平台判断
const isWeb = typeof window !== 'undefined';
const isNextJS = typeof process !== 'undefined' && !!process.env.NEXT_PUBLIC_BACKEND_API_URL && !isWeb;
const isReactNative = !isWeb && !isNextJS;

// 用于记录是否已经初始化
let initialized = false;
// 存储 cookie 模块实例
let cookiesModule: any = null;
// 标记 next headers cookies 是否可用
let nextHeadersCookiesAvailable = false;

/**
 * 初始化平台相关的模块
 */
const initPlatformModules = async () => {
  if (initialized) return;

  try {
    // Web 平台：导入 js-cookie
    if (isWeb) {
      const jsCoookies = await import('js-cookie');
      cookiesModule = jsCoookies.default;
    }

    // NextJS SSR：尝试导入 next/headers 的 cookies
    if (isNextJS) {
      try {
        // 在 SSR 环境中动态导入
        // 注意：next/headers 只能在服务端组件中使用
        // 在这里我们只是尝试导入，标记它是否可用
        await import('next/headers');
        nextHeadersCookiesAvailable = true;
      } catch (error) {
        console.warn('无法导入 next/headers，可能不在 NextJS 服务端环境中');
      }
    }
    
    initialized = true;
  } catch (error) {
    console.error('初始化平台模块失败:', error);
  }
};

// 在模块加载时启动初始化
initPlatformModules();

/**
 * 获取区域设置/语言
 * @returns 语言代码
 */
const getLocale = (): string => {
  try {
    // Web 环境：从浏览器 cookies 中读取
    if (isWeb && cookiesModule) {
      const locale = cookiesModule.get('Locale');
      return locale === 'zh-CN' ? 'zh-CN' : locale === 'en' ? 'en-US' : 'en-US';
    }

    // NextJS SSR 环境：从请求头中读取
    if (isNextJS && nextHeadersCookiesAvailable) {
      try {
        // 注意：这段代码只能在 NextJS 服务端组件中执行
        // 在拦截器中可能无法正常工作，可能需要在组件级别获取并传递
        const { cookies } = require('next/headers');
        const locale = cookies().get('Locale')?.value;
        return locale === 'zh-CN' ? 'zh-CN' : locale === 'en' ? 'en-US' : 'en-US';
      } catch (error) {
        console.warn('无法在 SSR 环境中获取 cookies', error);
        return 'en-US'; // 默认语言
      }
    }

    // React Native 环境：暂时返回默认值
    if (isReactNative) {
      // TODO: 从 AsyncStorage 或其他存储中获取语言设置
      return 'en-US'; // 默认语言
    }

    return 'en-US'; // 默认语言
  } catch (error) {
    console.error('获取语言设置失败:', error);
    return 'en-US'; // 发生错误时返回默认语言
  }
};

// 定义API响应的标准格式
export interface ApiResponse<T = any> {
  /**
   * 状态码，0表示成功，非0表示错误
   */
  status: number;
  /**
   * 状态描述或错误信息
   */
  msg?: string;
  /**
   * 响应数据主体
   */
  data?: T;
}

// 定义API错误类型
export interface ApiError {
  /**
   * 错误状态码
   */
  status: number;
  /**
   * 错误信息
   */
  msg: string;
  /**
   * 错误相关数据
   */
  data?: any;
}
let baseUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || '/'
if (isWeb) {
  if (process.env.APP_ENV === 'production') {
    `https://api.${}`
  }
}

// 创建axios实例
const axiosInstance: AxiosInstance = axios.create({
  baseURL: baseUrl,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 在发送请求之前做些什么，例如添加token
    const token = authStore.currentToken;

    if (token) {
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加语言头信息
    const locale = getLocale();
    if (locale) {
      config.headers = config.headers || {};
      config.headers.Locale = locale;
    }

  const isGray = process.env.SETUP_ENV === 'gray';

  if (isNextJS && isGray) {
    config.headers = config.headers || {};
    config.headers['x-gray'] = 'true';
  }

    return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  },
);

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    // return response.data.data;
    // 对响应数据做点什么
    const responseData = response.data as ApiResponse;
    // 当status为0时返回data字段，否则返回整个响应对象
    // 这样成功请求直接获得数据，错误请求可以获取完整的错误信息
    if (responseData.status === 0 && responseData.data !== undefined) {
      return responseData.data;
    } else if (responseData.status === 401) {
      userStore.logout(authStore.activeUserId);
    }

    // 当status非0时，以reject方式返回错误
    return Promise.reject<ApiError>({
      status: responseData.status,
      msg: responseData.msg || 'request failed',
      data: responseData.data,
    });
  },
  (error) => {
    const responseData = error.response?.data as ApiResponse;
    if (error.response?.status === 401 || error.response?.data?.status === 401) {
      userStore.logout(authStore.activeUserId);
      return Promise.reject<ApiError>({
        status: responseData.status,
        msg: responseData.msg || 'request failed',
        data: responseData.data,
      });
    }
    // 对响应错误做点什么
    else if (error.response) {
      // 请求已发出，但服务器响应的状态码不在 2xx 范围内
      return Promise.reject<ApiError>({
        status: error.response?.status || -1,
        msg: error.response?.data?.msg || 'server response error',
        data: error.response?.data,
      });
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      return Promise.reject<ApiError>({
        status: -1,
        msg: 'network request failed, please check your network connection',
        data: error.request,
      });
    } else {
      // 在设置请求时发生了一些事情，触发了错误
      return Promise.reject<ApiError>({
        status: -2,
        msg: error.message || 'request configuration error',
        data: error,
      });
    }
  },
);

// 封装GET请求
const get = <T extends ApiResponse>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T['data']> => {
  return axiosInstance.get<T, T['data']>(url, { params, ...config });
};

// 封装POST请求
const post = <T extends ApiResponse>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T['data']> => {
  return axiosInstance.post<T, T['data']>(url, data, config);
};

// 封装PUT请求
const put = <T extends ApiResponse>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T['data']> => {
  return axiosInstance.put<T, T['data']>(url, data, config);
};

// 封装DELETE请求
const del = <T extends ApiResponse>(url: string, config?: AxiosRequestConfig): Promise<T['data']> => {
  return axiosInstance.delete<T, T['data']>(url, config);
};

// 对外暴露的api对象
const api = {
  get,
  post,
  put,
  delete: del,
  instance: axiosInstance,
};

export default api;
