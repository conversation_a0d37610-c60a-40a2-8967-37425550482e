'use client';

import { ScrollView } from '@quantum/components/ui/scroll-view';
import HeroSection from '../components/HeroSection';
import DescriptionLine from '../components/DescriptionLine/DescriptionLine';
import DescriptionLineIcon1 from '@quantum/shared/assets/features/watchlists/DescriptionLineIcon1.png';
import Image from '@unitools/image';
import DescriptionCard from '../components/DescriptionCard/DescriptionCard';
import { useTranslation } from 'react-i18next';

import watchlist1 from '@quantum/shared/assets/features/watchlists/watchlists-1.svg';
import watchlist2 from '@quantum/shared/assets/features/watchlists/watchlists-2.svg';
import watchlist3 from '@quantum/shared/assets/features/watchlists/watchlists-3.svg';
import watchlist4 from '@quantum/shared/assets/features/watchlists/watchlists-4.svg';
import { useServerInfo } from 'next-app/provider/ServerInfoProvider';

export default function Watchlists() {
  const { t } = useTranslation();

  const serverInfo = useServerInfo()
  return (
    <ScrollView className="bg-white">
      <HeroSection
        title={t('features_page.watchlists.hero.title')}
        subtitle={t('features_page.watchlists.hero.subtitle')}
        description={t('features_page.watchlists.hero.description')}
        ctaText={t('features_page.watchlists.hero.cta')}
        imageUrl={`${serverInfo.staticDomain}/o/Frame%201410077139-5gwzvj.png`}
      />

      <DescriptionCard
        title={null}
        features={[
          {
            title: t('features_page.watchlists.features.add_asset.title'),
            subtitle: t('features_page.watchlists.features.add_asset.subtitle'),
            image: watchlist1,
            descriptions: [
              t('features_page.watchlists.features.add_asset.desc1'),
              t('features_page.watchlists.features.add_asset.desc2'),
              t('features_page.watchlists.features.add_asset.desc3'),
            ],
          },
          {
            title: t('features_page.watchlists.features.price_alerts.title'),
            subtitle: t('features_page.watchlists.features.price_alerts.subtitle'),
            image: watchlist2,
            descriptions: [
              t('features_page.watchlists.features.price_alerts.desc1'),
              t('features_page.watchlists.features.price_alerts.desc2'),
              t('features_page.watchlists.features.price_alerts.desc3'),
            ],
          },
          {
            title: t('features_page.watchlists.features.tracking.title'),
            subtitle: t('features_page.watchlists.features.tracking.subtitle'),
            image: watchlist3,
            descriptions: [
              t('features_page.watchlists.features.tracking.desc1'),
              t('features_page.watchlists.features.tracking.desc2'),
              t('features_page.watchlists.features.tracking.desc3'),
              t('features_page.watchlists.features.tracking.desc4'),
            ],
          },
          {
            title: t('features_page.watchlists.features.cross_market.title'),
            subtitle: t('features_page.watchlists.features.cross_market.subtitle'),
            image: watchlist4,
            descriptions: [
              t('features_page.watchlists.features.cross_market.desc1'),
              t('features_page.watchlists.features.cross_market.desc2'),
              t('features_page.watchlists.features.cross_market.desc3'),
            ],
          },
        ]}
      />

      <DescriptionLine
        title={t('features_page.watchlists.why_choose.title')}
        features={[
          {
            icon: <Image source={DescriptionLineIcon1} alt="AI Powered" width={32} height={32} />,
            title: t('features_page.watchlists.why_choose.feature1'),
            description: '',
          },
          {
            icon: <Image source={DescriptionLineIcon1} alt="AI Powered" width={32} height={32} />,
            title: t('features_page.watchlists.why_choose.feature2'),
            description: '',
          },
          {
            icon: <Image source={DescriptionLineIcon1} alt="AI Powered" width={32} height={32} />,
            title: t('features_page.watchlists.why_choose.feature3'),
            description: '',
          },
          {
            icon: <Image source={DescriptionLineIcon1} alt="AI Powered" width={32} height={32} />,
            title: t('features_page.watchlists.why_choose.feature4'),
            description: '',
          },
        ]}
      />
    </ScrollView>
  );
}
