import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ModalCloseButton,
  ModalBody,
} from '@quantum/components/ui/modal';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { RiCloseCircleFill, RiCloseFill, RiStarLine, RiStarSFill } from 'react-icons/ri';
import { observer } from 'mobx-react-lite';
import { VStack } from '@quantum/components/ui/vstack';
import { HStack } from '@quantum/components/ui/hstack';
import { Button, ButtonText } from '@quantum/components/ui/button';
import { useRequest } from 'ahooks';
import { batchAddWatchlist } from '../../../../services/watchlist/watchlist.api';
import { portfolioStore } from '../../../../store/portfolio.store';
import { watchlistStore } from '../../../../store/watchlist.store';
import { sleep } from '../../../../uitls';
import { useCustomToast } from '../../../../components/Toast/ToastProvider';
import { Divider } from '@quantum/components/ui/divider';
import { Checkbox } from '@quantum/components/ui/checkbox';
import { Market, MarketTrendingSort, type TrendingItem } from '../../../../services/asset/asset.types';
import { Pressable } from '@quantum/components/ui/pressable';
import { useEffect, useMemo, useState } from 'react';
import { getMarketTrending } from '../../../../services/asset/asset.api';
import { useTranslation } from 'react-i18next';
import { Skeleton } from '@quantum/components/ui/skeleton';
import Empty from '../../../../components/Empty/Empty';
import TextOrImageLogo from '../../../../components/TextOrImageLogo';
import { ScrollView } from '@quantum/components/ui/scroll-view';
import { Image } from '@quantum/components/ui/image';
import { useHandleErrorMessage } from '../../../../utils/handleErrorMessage';
import { useServerInfo } from 'next-app/provider/ServerInfoProvider';

function SuggestionAssetItem({
  item,
  onSelect,
  selectedList,
}: {
  item: TrendingItem;
  onSelect?: (item: TrendingItem) => void;
  selectedList: TrendingItem[];
}) {
  const [checked, setChecked] = useState(false);
  const isSelected = useMemo(() => {
    return selectedList.some((_) => _.symbol === item.symbol && _.market === item.market);
  }, [selectedList]);
  const isDisabled = useMemo(() => {
    const list = watchlistStore.watchlist.data || [];
    return list.some((_) => _.symbol === item.symbol && _.market === item.market);
  }, [watchlistStore.watchlist.data]);

  function handleSelect() {
    onSelect?.(item);
  }



  return (
    <HStack className="gap-[8px] items-center justify-between hover:bg-[#F0F0F0]">
      <Box className="flex-1">
        <HStack className="gap-[8px] items-center flex-1 w-full">
          <TextOrImageLogo text={item.name} size={24} />
          <Text className="text-[#0A0A0A] text-[14px] font-500 leading-[20px]">{item.name}</Text>
          <Text className="text-[#808080] text-[14px] font-400 leading-[20px]">{item.symbol}</Text>
        </HStack>
      </Box>

      {isDisabled ? (
        <RiStarSFill size={20} className={`text-[#f5b800] cursor-not-allowed`} />
      ) : (
        <>
          <Pressable
            onPress={() => {
              handleSelect();
            }}
          >
            {isSelected ? (
              <RiStarSFill size={20} className={`text-[#f5b800]`} />
            ) : (
              <RiStarLine size={20} className="text-[#808080]" />
            )}
          </Pressable>
        </>
      )}
    </HStack>
  );
}

export const SuggestionAssetItemComp = observer(SuggestionAssetItem);

function SuggestionForm({ submit, submitLoading }: { submit: (list: TrendingItem[]) => void; submitLoading: boolean }) {
  const { t, i18n } = useTranslation();
  const [selectedTab, setSelectedTab] = useState<Market>(Market.CRYPTO);

  const serverInfo = useServerInfo()
  const tabs = [
    {
      label: t('portfolio_page.suggestion.crypto'),
      value: Market.CRYPTO,
      bg: `${serverInfo.staticDomain}/o/cypto-4nxnrg.png`,
    },
    {
      label: t('portfolio_page.suggestion.us_stocks'),
      value: Market.US,
      bg: `${serverInfo.staticDomain}/o/us-mq7m8y.png`,
    },
    {
      label: t('portfolio_page.suggestion.hk_stocks'),
      value: Market.HK,
      bg: `${serverInfo.staticDomain}/o/hk-860ls3.png`,
    },
  ];

  const { data: cryptoList = [], loading: cryptoLoading } = useRequest(
    async () => {
      try {
        const res = await getMarketTrending(Market.CRYPTO, MarketTrendingSort.VALUE_DESC);
        return res || [];
      } catch (error) {
        return [];
      }
    },
    {
      refreshDeps: [i18n.language],
    },
  );

  const { data: usList = [], loading: usLoading } = useRequest(
    async () => {
      try {
        const res = await getMarketTrending(Market.US, MarketTrendingSort.VALUE_DESC);
        return res || [];
      } catch (error) {
        return [];
      }
    },
    {
      refreshDeps: [i18n.language],
    },
  );

  const { data: hkList = [], loading: hkLoading } = useRequest(
    async () => {
      try {
        const res = await getMarketTrending(Market.HK, MarketTrendingSort.VALUE_DESC);
        return res || [];
      } catch (error) {
        return [];
      }
    },
    {
      refreshDeps: [i18n.language],
    },
  );

  const list = useMemo(() => {
    let res: TrendingItem[] = [];
    if (selectedTab === Market.CRYPTO) {
      res = cryptoList;
    } else if (selectedTab === Market.US) {
      res = usList;
    } else if (selectedTab === Market.HK) {
      res = hkList;
    }
    return res;
  }, [selectedTab, cryptoList, usList, hkList]);

  const smallList = useMemo(() => {
    return list.slice(0, 8);
  }, [list]);
  const canShowMore = useMemo(() => {
    return !!list.length;
  }, [list]);

  const loading = useMemo(() => {
    if (selectedTab === Market.CRYPTO) {
      return cryptoLoading;
    } else if (selectedTab === Market.US) {
      return usLoading;
    } else if (selectedTab === Market.HK) {
      return hkLoading;
    }
    return false
  }, [cryptoLoading, usLoading, hkLoading]);

  const [showMore, setShowMore] = useState(false);
  const [selectedList, setSelectedList] = useState<TrendingItem[]>([]);

  useEffect(() => {
    setSelectedList((pre: TrendingItem[]) => {
      const list = (watchlistStore.watchlist.data || []).map((_) => ({
        name: _.name,
        symbol: _.symbol,
        market: _.market,
        price: _.price,
        value: '0',
        market_cap: '0',
        change_rate: _.change_rate || '0',
      }));
      const ary = pre.filter((_) => !list.some((__) => _.symbol === _.symbol && _.market === _.market));
      return [...ary, ...list];
    });
  }, [watchlistStore.watchlist.data]);
  function handleSelect(item: TrendingItem) {
    setSelectedList((pre) => {
      if (pre.some((_) => _.symbol === item.symbol && _.market === item.market)) {
        const ary = pre.filter((_) => _.symbol !== item.symbol || _.market !== item.market);
        return [...ary];
      }
      return [...pre, item];
    });
  }
  function handleSubmit() {
    submit(selectedList);
  }
  return (
    <>
      <Box>
        <VStack className="gap-6 px-[24px] pb-[24px]">
          <Text className="text-[#808080] text-[16px] font-400 leading-[20px]">
            {t('portfolio_page.suggestion.interest_description')}
          </Text>
          <Divider className="bg-[#e6e6e6]" />
          {showMore ? (
            <>
              <ScrollView className=" max-h-[300px] overflow-auto">
                <VStack className="gap-4">
                  {loading ? (
                    <>
                      {new Array(3).fill(undefined).map((_, index) => (
                        <HStack className="gap-[8px] items-center justify-between hover:bg-[#F0F0F0]" key={index}>
                          <HStack className="gap-[8px] items-center flex-1 w-full">
                            <Skeleton variant="circular" className="h-[24px] w-[24px]" />

                            <Skeleton className="h-[20px] w-[90px]" />
                            <Skeleton className="h-[20px] w-[70px]" />
                          </HStack>

                          <Skeleton variant="circular" className="h-[16px] w-[16px]" />
                        </HStack>
                      ))}
                    </>
                  ) : (
                    <>
                      {list.length > 0 ? (
                        <>
                          {list.map((item, index) => (
                            <SuggestionAssetItem
                              key={index}
                              item={item}
                              onSelect={handleSelect}
                              selectedList={selectedList}
                            />
                          ))}
                        </>
                      ) : (
                        <Empty content={t('portfolio_page.suggestion.no_data')} />
                      )}
                    </>
                  )}
                </VStack>
              </ScrollView>
            </>
          ) : (
            <>
              <VStack className="gap-2">
                <Text className="text-[#0A0A0A] text-[16px] font-500 leading-[20px]">
                  {t('portfolio_page.suggestion.type')}
                </Text>
                <HStack className="gap-3">
                  {tabs.map((tab) => (
                    <Pressable key={tab.value} className="flex-1" onPress={() => setSelectedTab(tab.value)}>
                      <VStack
                        className={`p-4 h-[141px] bg-[#FFF] rounded-[8px] border-[2px] border-solid border-[#fff] relative overflow-hidden ${
                          selectedTab === tab.value ? 'border-[#05C697]' : 'border-[#fff]'
                        }`}
                      >
                        {selectedTab === tab.value && (
                          <Box className="absolute bottom-0 right-0 w-full h-[100px] z-[1-] bg-gradient-to-b from-[#FFF] to-[#DEF2ED]"></Box>
                        )}
                        <Text className="text-[#0A0A0A] text-[14px] font-500 leading-[20px] relative z-[1]">
                          {tab.label}
                        </Text>
                        <Image
                          source={{
                            uri: tab.bg,
                          }}
                          className="w-full h-full absolute bottom-0 left-0 z-[0]"
                        />
                      </VStack>
                    </Pressable>
                  ))}
                </HStack>
              </VStack>

              <VStack className="gap-2">
                <Text className="text-[#0A0A0A] text-[16px] font-500 leading-[20px]">
                  {t('portfolio_page.suggestion.currency')}
                </Text>
                <HStack className="gap-2 flex-wrap">
                  {smallList.map((tab, index) => {
                    const isSelected = selectedList.some((_) => _.symbol === tab.symbol && _.market === tab.market);
                    const content = (
                      <HStack
                        className={`gap-[2px] flex px-[12px] py-[6px] justify-center items-center gap-[4px] rounded-[24px] bg-[rgba(5,198,151,0.10)] ${
                          isSelected ? 'bg-[#05C697]' : ''
                        }`}
                      >
                        <Text
                          className={`text-[#05C697] text-[14px] font-500 leading-[20px] ${
                            isSelected ? 'text-[#fff]' : ''
                          }`}
                        >
                          {tab.name}
                        </Text>
                        <Pressable onPress={() => handleSelect(tab)}>
                          {isSelected && <RiCloseCircleFill size={12} className="text-[#fff]" />}
                        </Pressable>
                      </HStack>
                    );
                    if (isSelected) {
                      return content;
                    }
                    return (
                      <Pressable key={index} onPress={() => handleSelect(tab)}>
                        {content}
                      </Pressable>
                    );
                  })}
                  {canShowMore && (
                    <Pressable onPress={() => setShowMore(true)}>
                      <HStack className="flex px-[12px] py-[6px] justify-center items-center gap-[2px] rounded-[24px] bg-[#E6E6E6]">
                        <Text className="text-[#0A0A0A] text-[14px] font-500 leading-[20px]">
                          {t('portfolio_page.suggestion.show_more')}{' '}
                        </Text>
                        <Text className="text-[#0A0A0A] text-[14px] font-500 leading-[20px] mt-[-4px]">+</Text>
                      </HStack>
                    </Pressable>
                  )}
                </HStack>
              </VStack>
            </>
          )}
        </VStack>
        <HStack className="h-[84px] gap-[10px] border-t border-solid border-[#E6E6E6] items-center justify-end px-[24px]">
          {showMore ? (
            <>
              <Button
                className="h-[48px] px-[28px] rounded-[8px] bg-[#05C697]"
                onPress={() => {
                  setShowMore(false);
                }}
                loading={submitLoading}
              >
                <ButtonText className="text-[#FFF] text-[16px] font-600 leading-[20px]">
                  {t('portfolio_page.suggestion.previous')}
                </ButtonText>
              </Button>

              <Button
                className="h-[48px] px-[28px] rounded-[8px] bg-[#05C697]"
                onPress={() => {
                  handleSubmit();
                }}
                loading={submitLoading}
              >
                <ButtonText className="text-[#FFF] text-[16px] font-600 leading-[20px]">
                  {t('portfolio_page.suggestion.finish')}
                </ButtonText>
              </Button>
            </>
          ) : (
            <Button
              className="h-[48px] px-[28px] rounded-[8px] bg-[#05C697]"
              isDisabled={!canShowMore}
              onPress={() => {
                setShowMore(true);
              }}
            >
              <ButtonText className="text-[#FFF] text-[16px] font-600 leading-[20px]">
                {t('portfolio_page.suggestion.next')}
              </ButtonText>
            </Button>
          )}
        </HStack>
      </Box>
    </>
  );
}
const SuggestionFormComp = observer(SuggestionForm);

export default function SuggestionModal({
  show,
  onClose,
  onFinish,
}: {
  show: boolean;
  onClose: () => void;
  onFinish: () => void;
}) {
  const { showToast } = useCustomToast();
  const { t } = useTranslation();

  const { handleErrorMessage } = useHandleErrorMessage();
  const { loading: submitLoading, run: submit } = useRequest(
    async (list: TrendingItem[]) => {
      try {
        const res = await batchAddWatchlist({
          assets: list.map((_) => ({
            market: _.market,
            symbol: _.symbol,
          })),
        });
        onFinish();
        await sleep(3000);
        watchlistStore.watchlist.reload();
        onClose();
        return res;
      } catch (error: any) {
        handleErrorMessage(error)

        return null;
      }
    },
    {
      manual: true,
    },
  );

  return (
    <Modal isOpen={show} onClose={onClose} className="fixed inset-0">
      <ModalBackdrop className="bg-[#000000] opacity-70" />
      <ModalContent
        size="full"
        className="bg-[#F7F7F7] p-0 pt-[32px]  max-w-[600px] w-full min-h-[320px]  rounded-[16px] shadow-[0px_4px_25px_0px_rgba(0,0,0,0.10),0px_4px_10px_0px_rgba(0,0,0,0.04)]"
      >
        <ModalHeader className="px-[24px]">
          <Box>
            <Text className="text-[#0A0A0A] text-[20px] font-600 leading-[24px]">
              {t('portfolio_page.suggestion.title')}
            </Text>
          </Box>
          <ModalCloseButton className="w-[24px] h-[24px] items-center justify-center border rounded-[6px] border-solid border-[#cccccc]">
            <RiCloseFill size={16} className="text-[#0a0a0a]" />
          </ModalCloseButton>
        </ModalHeader>
        <ModalBody className="mt-3 mb-0  pb-0">
          <SuggestionFormComp submit={submit} submitLoading={submitLoading} />
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}
