'use client';
import { Box } from '@quantum/components/ui/box';
import { Text } from '@quantum/components/ui/text';
import { VStack } from '@quantum/components/ui/vstack';
import { useRequest } from 'ahooks';
import { observer } from 'mobx-react-lite';
import { authStore } from '../../store/auth.store';
import { waitlistStore } from '../../store/waitlist.store';
import Interests from './Interests';
import UserInfo from './join/UserInfo';
import PremiumFreeWaitlist from './PremiumFreeWaitlist';
import WaitListPlans from './WaitListPlans';
import { Trans, useTranslation } from 'react-i18next';
import { TypeWriter } from '../../components/TypeWriter';
import { HStack } from '@quantum/components/ui/hstack';
import { useState, useMemo } from 'react';
import TypeWriterTitle from './TypeWriterTitle';
import { useServerInfo } from 'next-app/provider/ServerInfoProvider';
function WaitListIndex() {
  const { t } = useTranslation();
  const { data: waitlistStatus } = useRequest(
    async () => {
      if (!authStore.isLoggedIn) {
        waitlistStore.waitlistStatus.setData(null);
        return;
      }
      const res = await waitlistStore.waitlistStatus.fetch();
      return res;
    },
    {
      refreshDeps: [authStore.isLoggedIn, authStore.activeUserId],
    },
  );

  return (
    <Box className="pt-[100px] pb-[100px]">
      <Text className="mx-auto max-w-[663px] text-[#0A0A0A] text-center  text-[50px] font-not-italic font-[800] leading-[62px]">
        
      <TypeWriterTitle />
      </Text>
      <Text className="mt-5 mx-auto max-w-[618px] text-[#808080] text-center text-[18px] font-not-italic font-[400] leading-[30px]">
        {t('waitlist.home_page.description')}
      </Text>
      <VStack className="gap-[140px] mt-[80px]">
        <Box className="bg-[#fff] rounded-[18px]">
          {waitlistStore.waitlistStatus.data?.result ? <UserInfo /> : <PremiumFreeWaitlist />}
        </Box>
        <WaitListPlans />
        <Interests />
      </VStack>
    </Box>
  );
}

export default observer(WaitListIndex);
